"use client";

import { Flex, Table, TextField } from "@radix-ui/themes";
import { useEffect } from "react";

export default function Home() {
  const messages = [
    {
      content: "Hello, how are you?",
      role: "user",
    },
    {
      content: "I am doing well, thank you. How can I help you?",
      role: "assistant",
    },
  ];

  const handleSend = async (message: string) => {
    const response = await fetch("/api/chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        messages: [
          ...messages,
          {
            content: message,
            role: "user",
          },
        ],
      }),
    });

    const data = await response.json();
    console.log(data);
  };

  useEffect(() => {
    handleSend("Hello, how are you?");
  }, []);

  return (
    <div>
      <TextField.Root placeholder="Search the docs…"></TextField.Root>
      <Flex direction="row" gap="3" className="h-screen">
        <Table.Root className="flex-1">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Full name</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Email</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Group</Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            <Table.Row>
              <Table.RowHeaderCell>Danilo Sousa</Table.RowHeaderCell>
              <Table.Cell><EMAIL></Table.Cell>
              <Table.Cell>Developer</Table.Cell>
            </Table.Row>

            <Table.Row>
              <Table.RowHeaderCell>Zahra Ambessa</Table.RowHeaderCell>
              <Table.Cell><EMAIL></Table.Cell>
              <Table.Cell>Admin</Table.Cell>
            </Table.Row>

            <Table.Row>
              <Table.RowHeaderCell>Jasper Eriksson</Table.RowHeaderCell>
              <Table.Cell><EMAIL></Table.Cell>
              <Table.Cell>Developer</Table.Cell>
            </Table.Row>
          </Table.Body>
        </Table.Root>
        <div className="w-100 bg-white"></div>
      </Flex>
    </div>
  );
}
